import { Store } from "@tanstack/react-store";
import type { Client } from "~/modules/client/service/model/client";
import type { Schedule, Turn } from "~/modules/schedule/service/model/schedule";
import type { Worker } from "~/modules/worker/service/model/worker";

interface SessionState {
	selectedSchedule: Schedule | null;
	selectedTurn: Turn | null;
	selectedClient: Client | null;
	selectedWorker: Worker | null;
	seeAll: boolean;
	showNames: boolean;
	// For selective name display when showNames is false
	nameDisplayClient: Client | null;
	nameDisplayWorker: Worker | null;
}

const initialState: SessionState = {
	selectedSchedule: null,
	selectedTurn: null,
	selectedClient: null,
	selectedWorker: null,
	seeAll: false,
	showNames: true,
	nameDisplayClient: null,
	nameDisplayWorker: null,
};

export const sessionStore = new Store<SessionState>(initialState);

export const sessionActions = {
	setSchedule: (schedule: Schedule | null) => {
		sessionStore.setState((prev) => ({
			...prev,
			selectedSchedule: schedule,
			selectedTurn: null,
			selectedClient: null,
			selectedWorker: null,
		}));
	},
	setTurn: (turn: Turn | null) => {
		sessionStore.setState((prev) => ({
			...prev,
			selectedTurn: turn,
			selectedClient: null,
			selectedWorker: null,
		}));
	},
	setClient: (client: Client | null) => {
		sessionStore.setState((prev) => ({
			...prev,
			selectedClient: client,
			// If a client is selected, clear the worker and disable see all
			selectedWorker: client ? null : prev.selectedWorker,
			seeAll: client ? false : prev.seeAll,
		}));
	},
	setWorker: (worker: Worker | null) => {
		sessionStore.setState((prev) => ({
			...prev,
			selectedWorker: worker,
			// If a worker is selected, clear the client and disable see all
			selectedClient: worker ? null : prev.selectedClient,
			seeAll: worker ? false : prev.seeAll,
		}));
	},
	setSeeAll: (seeAll: boolean) => {
		sessionStore.setState((prev) => ({
			...prev,
			seeAll,
			// If enabling see all, clear client and worker selections
			selectedClient: seeAll ? null : prev.selectedClient,
			selectedWorker: seeAll ? null : prev.selectedWorker,
		}));
	},
	setShowNames: (showNames: boolean) => {
		sessionStore.setState((prev) => ({
			...prev,
			showNames,
			// Clear name display selections when enabling show names
			nameDisplayClient: showNames ? null : prev.nameDisplayClient,
			nameDisplayWorker: showNames ? null : prev.nameDisplayWorker,
		}));
	},
	setNameDisplayClient: (client: Client | null) => {
		sessionStore.setState((prev) => ({
			...prev,
			nameDisplayClient: client,
			// Clear worker name display when client is selected
			nameDisplayWorker: client ? null : prev.nameDisplayWorker,
		}));
	},
	setNameDisplayWorker: (worker: Worker | null) => {
		sessionStore.setState((prev) => ({
			...prev,
			nameDisplayWorker: worker,
			// Clear client name display when worker is selected
			nameDisplayClient: worker ? null : prev.nameDisplayClient,
		}));
	},
	clearAll: () => {
		sessionStore.setState(initialState);
	},
};
